"use client";

import { motion } from "framer-motion";

export default function DemoSection() {
  return (
    <section className="demo">
      <div className="container">
        <motion.div
          className="section-header"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2>See PersonaRoll in Action</h2>
          <p>Watch how AI transforms your photos into viral content</p>
        </motion.div>

        <div className="demo-grid">
          <motion.div
            className="demo-card"
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
          >
            <div className="demo-header">
              <h3>📸 Camera Roll Analysis</h3>
              <span className="demo-status">Analyzing...</span>
            </div>
            <div className="demo-content">
              <div className="photo-grid">
                <div className="photo-item">🏖️</div>
                <div className="photo-item">🍕</div>
                <div className="photo-item">🎵</div>
                <div className="photo-item">🏃‍♂️</div>
              </div>
              <div className="analysis-results">
                <div className="insight">
                  <span className="insight-label">Lifestyle:</span>
                  <span className="insight-value">Active & Social</span>
                </div>
                <div className="insight">
                  <span className="insight-label">Interests:</span>
                  <span className="insight-value">Travel, Food, Music</span>
                </div>
                <div className="insight">
                  <span className="insight-label">Aesthetic:</span>
                  <span className="insight-value">Vibrant & Authentic</span>
                </div>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="demo-card"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <div className="demo-header">
              <h3>🎭 Persona Generation</h3>
              <span className="demo-status">Complete</span>
            </div>
            <div className="demo-content">
              <div className="persona-list">
                <div className="persona-item active">
                  <div className="persona-avatar">🌟</div>
                  <div className="persona-info">
                    <h4>The Trendsetter</h4>
                    <p>Bold, confident, always ahead of the curve</p>
                  </div>
                </div>
                <div className="persona-item">
                  <div className="persona-avatar">🎨</div>
                  <div className="persona-info">
                    <h4>The Creative</h4>
                    <p>Artistic, thoughtful, inspiring content</p>
                  </div>
                </div>
                <div className="persona-item">
                  <div className="persona-avatar">😄</div>
                  <div className="persona-info">
                    <h4>The Friend</h4>
                    <p>Relatable, funny, authentic storytelling</p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="demo-card"
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
          >
            <div className="demo-header">
              <h3>✨ Content Generation</h3>
              <span className="demo-status">Generating...</span>
            </div>
            <div className="demo-content">
              <div className="generated-post">
                <div className="post-header">
                  <div className="post-avatar">🌟</div>
                  <div className="post-info">
                    <h4>The Trendsetter</h4>
                    <span>2 minutes ago</span>
                  </div>
                </div>
                <div className="post-content">
                  <p>
                    "Just discovered this hidden gem 🏖️ Sometimes the best
                    adventures happen when you least expect them! Who else loves
                    spontaneous beach days? #BeachVibes #Spontaneous
                    #LifeIsGood"
                  </p>
                  <div className="post-stats">
                    <span>❤️ 1.2K</span>
                    <span>💬 89</span>
                    <span>🔄 234</span>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
